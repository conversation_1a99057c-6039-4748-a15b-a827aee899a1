package integral

import (
	"errors"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	integralRes "github.com/flipped-aurora/gin-vue-admin/server/model/integral/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type FeedbackService struct{}

var FeedbackServiceApp = new(FeedbackService)

// CreateFeedback 创建反馈
func (feedbackService *FeedbackService) CreateFeedback(userID uint, req integralReq.CreateFeedbackRequest) (feedback integral.Feedback, err error) {
	feedback = integral.Feedback{
		UserID:     userID,
		ProjectsID: req.ProjectsID,
		Title:      req.Title,
		Content:    req.Content,
		Type:       req.Type,
		Status:     integral.FeedbackStatusPending,
		Tags:       req.Tags,
	}

	err = global.GVA_DB.Create(&feedback).Error
	if err != nil {
		global.GVA_LOG.Error("创建反馈失败", zap.Error(err))
		return feedback, fmt.Errorf("创建反馈失败: %v", err)
	}

	global.GVA_LOG.Info("用户创建反馈", zap.Uint("userID", userID), zap.String("title", req.Title))
	return feedback, nil
}

// UpdateFeedback 更新反馈（仅限用户自己的反馈且未审核通过）
func (feedbackService *FeedbackService) UpdateFeedback(userID uint, req integralReq.UpdateFeedbackRequest) (err error) {
	var feedback integral.Feedback
	err = global.GVA_DB.Where("id = ? AND user_id = ?", req.ID, userID).First(&feedback).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("反馈不存在或无权限修改")
		}
		return fmt.Errorf("查询反馈失败: %v", err)
	}

	// 只有待审核状态的反馈才能修改
	if feedback.Status != integral.FeedbackStatusPending {
		return errors.New("只有待审核的反馈才能修改")
	}

	updates := map[string]interface{}{
		"title":   req.Title,
		"content": req.Content,
		"type":    req.Type,
		"tags":    req.Tags,
	}

	err = global.GVA_DB.Model(&feedback).Updates(updates).Error
	if err != nil {
		global.GVA_LOG.Error("更新反馈失败", zap.Error(err))
		return fmt.Errorf("更新反馈失败: %v", err)
	}

	global.GVA_LOG.Info("用户更新反馈", zap.Uint("userID", userID), zap.Uint("feedbackID", req.ID))
	return nil
}

// DeleteFeedback 删除反馈（仅限用户自己的反馈且未审核通过）
func (feedbackService *FeedbackService) DeleteFeedback(userID uint, feedbackID uint) (err error) {
	var feedback integral.Feedback
	err = global.GVA_DB.Where("id = ? AND user_id = ?", feedbackID, userID).First(&feedback).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("反馈不存在或无权限删除")
		}
		return fmt.Errorf("查询反馈失败: %v", err)
	}

	// 只有待审核状态的反馈才能删除
	if feedback.Status != integral.FeedbackStatusPending {
		return errors.New("只有待审核的反馈才能删除")
	}

	err = global.GVA_DB.Delete(&feedback).Error
	if err != nil {
		global.GVA_LOG.Error("删除反馈失败", zap.Error(err))
		return fmt.Errorf("删除反馈失败: %v", err)
	}

	global.GVA_LOG.Info("用户删除反馈", zap.Uint("userID", userID), zap.Uint("feedbackID", feedbackID))
	return nil
}

// GetUserFeedbackList 获取用户自己的反馈列表
func (feedbackService *FeedbackService) GetUserFeedbackList(userID uint, info integralReq.FeedbackSearch) (list []integral.Feedback, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&integral.Feedback{}).Where("user_id = ?", userID)

	// 添加搜索条件
	if info.Title != "" {
		db = db.Where("title LIKE ?", "%"+info.Title+"%")
	}
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}
	if info.Status != "" {
		db = db.Where("status = ?", info.Status)
	}
	if info.StartCreatedAt != nil && *info.StartCreatedAt != "" {
		db = db.Where("created_at >= ?", *info.StartCreatedAt)
	}
	if info.EndCreatedAt != nil && *info.EndCreatedAt != "" {
		db = db.Where("created_at <= ?", *info.EndCreatedAt)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&list).Error
	if err != nil {
		return
	}

	// 填充项目信息
	for i := range list {
		if list[i].ProjectsID != nil && *list[i].ProjectsID > 0 {
			var project mcp.Projects
			if err := global.GVA_DB.Select("id, name").Where("id = ?", *list[i].ProjectsID).First(&project).Error; err == nil {
				list[i].ProjectName = project.Name
			}
		}
	}

	return list, total, err
}

// GetPublicFeedbackList 获取公开的反馈列表（所有用户可见）
func (feedbackService *FeedbackService) GetPublicFeedbackList(info integralReq.FeedbackSearch) (list []integral.Feedback, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&integral.Feedback{}).Where("status = ? AND is_public = ?", integral.FeedbackStatusApproved, true)

	// 添加搜索条件
	if info.Title != "" {
		db = db.Where("title LIKE ?", "%"+info.Title+"%")
	}
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 关联用户信息
	err = db.Preload("User", func(db *gorm.DB) *gorm.DB {
		return db.Select("id, username, nick_name, header_img")
	}).Limit(limit).Offset(offset).Order("is_starred DESC, priority DESC, created_at DESC").Find(&list).Error

	// 填充用户信息到反馈对象中
	for i := range list {
		if list[i].User.ID > 0 {
			list[i].UserName = list[i].User.NickName
			if list[i].UserName == "" {
				list[i].UserName = list[i].User.Username
			}
			list[i].UserAvatar = list[i].User.HeaderImg
		}

		// 填充项目信息
		if list[i].ProjectsID != nil && *list[i].ProjectsID > 0 {
			var project mcp.Projects
			if err := global.GVA_DB.Select("id, name").Where("id = ?", *list[i].ProjectsID).First(&project).Error; err == nil {
				list[i].ProjectName = project.Name
			}
		}
	}

	return list, total, err
}

// GetAdminFeedbackList 获取管理员反馈列表（所有反馈）
func (feedbackService *FeedbackService) GetAdminFeedbackList(info integralReq.FeedbackSearch) (list []integral.Feedback, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&integral.Feedback{})

	// 添加搜索条件
	if info.Title != "" {
		db = db.Where("title LIKE ?", "%"+info.Title+"%")
	}
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}
	if info.Status != "" {
		db = db.Where("status = ?", info.Status)
	}
	if info.UserID > 0 {
		db = db.Where("user_id = ?", info.UserID)
	}
	if info.StartCreatedAt != nil && *info.StartCreatedAt != "" {
		db = db.Where("created_at >= ?", *info.StartCreatedAt)
	}
	if info.EndCreatedAt != nil && *info.EndCreatedAt != "" {
		db = db.Where("created_at <= ?", *info.EndCreatedAt)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 关联用户信息
	err = db.Preload("User", func(db *gorm.DB) *gorm.DB {
		return db.Select("id, username, nick_name, header_img")
	}).Limit(limit).Offset(offset).Order("created_at DESC").Find(&list).Error

	// 填充用户信息到反馈对象中
	for i := range list {
		if list[i].User.ID > 0 {
			list[i].UserName = list[i].User.NickName
			if list[i].UserName == "" {
				list[i].UserName = list[i].User.Username
			}
			list[i].UserAvatar = list[i].User.HeaderImg
		}

		// 填充项目信息
		if list[i].ProjectsID != nil && *list[i].ProjectsID > 0 {
			var project mcp.Projects
			if err := global.GVA_DB.Select("id, name").Where("id = ?", *list[i].ProjectsID).First(&project).Error; err == nil {
				list[i].ProjectName = project.Name
			}
		}

		// 如果有管理员ID，获取管理员名称
		if list[i].AdminUserID > 0 {
			var adminUser system.SysUser
			if err := global.GVA_DB.Select("username, nick_name").Where("id = ?", list[i].AdminUserID).First(&adminUser).Error; err == nil {
				list[i].AdminName = adminUser.NickName
				if list[i].AdminName == "" {
					list[i].AdminName = adminUser.Username
				}
			}
		}
	}

	return list, total, err
}

// ReviewFeedback 审核反馈
func (feedbackService *FeedbackService) ReviewFeedback(adminUserID uint, req integralReq.ReviewFeedbackRequest) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var feedback integral.Feedback
		err := tx.Where("id = ?", req.ID).First(&feedback).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("反馈不存在")
			}
			return fmt.Errorf("查询反馈失败: %v", err)
		}

		// 更新反馈状态
		updates := map[string]interface{}{
			"status":        req.Status,
			"is_starred":    req.IsStarred,
			"is_public":     req.IsPublic,
			"admin_reply":   req.AdminReply,
			"admin_user_id": adminUserID,
			"priority":      req.Priority,
			"remark":        req.Remark,
		}

		err = tx.Model(&feedback).Updates(updates).Error
		if err != nil {
			return fmt.Errorf("更新反馈失败: %v", err)
		}

		// 如果审核通过且标星，完成TASK_FEEDBACK任务并奖励积分
		if req.Status == integral.FeedbackStatusApproved && req.IsStarred {
			// 检查用户是否已经因为反馈获得过奖励
			var existingTask integral.SysUserTask
			err = tx.Where("task_id = ? AND ref_id = ? ",
				config.TASK_FEEDBACK, req.ID).
				First(&existingTask).Error

			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 没有获得过奖励，给予奖励
				var task integral.SysTask
				err = tx.Where("id = ?", config.TASK_FEEDBACK).First(&task).Error
				if err != nil {
					global.GVA_LOG.Error("查询反馈任务失败", zap.Error(err))
				} else {
					// 记录任务完成
					userTask := integral.SysUserTask{
						UserID: feedback.UserID,
						TaskID: config.TASK_FEEDBACK,
						Reward: task.Reward,
						Status: integral.UserTaskStatusCompleted,
						Remark: feedback.Title,
						RefId:  feedback.ID,
					}
					if err := tx.Create(&userTask).Error; err != nil {
						global.GVA_LOG.Error("记录反馈任务完成失败", zap.Error(err))
					} else {
						// 更新用户积分
						if err := tx.Model(&system.SysUser{}).Where("id = ?", feedback.UserID).
							UpdateColumn("points", gorm.Expr("points + ?", task.Reward)).Error; err != nil {
							global.GVA_LOG.Error("更新用户积分失败", zap.Error(err))
						} else {
							// 记录积分流水
							pointsRecord := integral.SysUserPoints{
								UserID:     feedback.UserID,
								Change:     task.Reward,
								Reason:     feedback.Title,
								Type:       "task",
								TaskID:     config.TASK_FEEDBACK,
								UserTaskID: userTask.ID,
							}
							if err := tx.Create(&pointsRecord).Error; err != nil {
								global.GVA_LOG.Error("记录积分流水失败", zap.Error(err))
							}
						}
					}
				}
			}
		}

		global.GVA_LOG.Info("管理员审核反馈",
			zap.Uint("adminUserID", adminUserID),
			zap.Uint("feedbackID", req.ID),
			zap.String("status", req.Status),
			zap.Bool("isStarred", req.IsStarred))

		return nil
	})
}

// LikeFeedback 点赞反馈
func (feedbackService *FeedbackService) LikeFeedback(userID uint, feedbackID uint) (err error) {
	// 增加点赞数和查看数
	err = global.GVA_DB.Model(&integral.Feedback{}).Where("id = ?", feedbackID).
		UpdateColumns(map[string]interface{}{
			"like_count": gorm.Expr("like_count + 1"),
			"view_count": gorm.Expr("view_count + 1"),
		}).Error

	if err != nil {
		global.GVA_LOG.Error("点赞反馈失败", zap.Error(err))
		return fmt.Errorf("点赞失败: %v", err)
	}

	global.GVA_LOG.Info("用户点赞反馈", zap.Uint("userID", userID), zap.Uint("feedbackID", feedbackID))
	return nil
}

// GetFeedbackByID 根据ID获取反馈详情
func (feedbackService *FeedbackService) GetFeedbackByID(feedbackID uint, userID uint) (feedback integral.Feedback, err error) {
	err = global.GVA_DB.Preload("User", func(db *gorm.DB) *gorm.DB {
		return db.Select("id, username, nick_name, header_img")
	}).Where("id = ?", feedbackID).First(&feedback).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return feedback, errors.New("反馈不存在")
		}
		return feedback, fmt.Errorf("查询反馈失败: %v", err)
	}

	// 检查权限：只有自己的反馈或公开的反馈才能查看
	if feedback.UserID != userID && (!feedback.IsPublic || feedback.Status != integral.FeedbackStatusApproved) {
		return feedback, errors.New("无权限查看此反馈")
	}

	// 填充用户信息
	if feedback.User.ID > 0 {
		feedback.UserName = feedback.User.NickName
		if feedback.UserName == "" {
			feedback.UserName = feedback.User.Username
		}
		feedback.UserAvatar = feedback.User.HeaderImg
	}

	// 如果有项目ID，获取项目信息
	if feedback.ProjectsID != nil && *feedback.ProjectsID > 0 {
		var project mcp.Projects
		if err := global.GVA_DB.Select("id, name").Where("id = ?", *feedback.ProjectsID).First(&project).Error; err == nil {
			feedback.ProjectName = project.Name
		}
	}

	// 如果有管理员ID，获取管理员名称
	if feedback.AdminUserID > 0 {
		var adminUser system.SysUser
		if err := global.GVA_DB.Select("username, nick_name").Where("id = ?", feedback.AdminUserID).First(&adminUser).Error; err == nil {
			feedback.AdminName = adminUser.NickName
			if feedback.AdminName == "" {
				feedback.AdminName = adminUser.Username
			}
		}
	}

	// 增加查看次数
	go func() {
		global.GVA_DB.Model(&integral.Feedback{}).Where("id = ?", feedbackID).
			UpdateColumn("view_count", gorm.Expr("view_count + 1"))
	}()

	return feedback, nil
}

// GetFeedbackStats 获取反馈统计信息
func (feedbackService *FeedbackService) GetFeedbackStats() (stats integralRes.FeedbackStatsResponse, err error) {
	// 总反馈数
	err = global.GVA_DB.Model(&integral.Feedback{}).Count(&stats.TotalCount).Error
	if err != nil {
		return
	}

	// 待审核数
	err = global.GVA_DB.Model(&integral.Feedback{}).Where("status = ?", integral.FeedbackStatusPending).Count(&stats.PendingCount).Error
	if err != nil {
		return
	}

	// 已通过数
	err = global.GVA_DB.Model(&integral.Feedback{}).Where("status = ?", integral.FeedbackStatusApproved).Count(&stats.ApprovedCount).Error
	if err != nil {
		return
	}

	// 已拒绝数
	err = global.GVA_DB.Model(&integral.Feedback{}).Where("status = ?", integral.FeedbackStatusRejected).Count(&stats.RejectedCount).Error
	if err != nil {
		return
	}

	// 标星数
	err = global.GVA_DB.Model(&integral.Feedback{}).Where("is_starred = ?", true).Count(&stats.StarredCount).Error
	if err != nil {
		return
	}

	// 公开数
	err = global.GVA_DB.Model(&integral.Feedback{}).Where("is_public = ?", true).Count(&stats.PublicCount).Error
	if err != nil {
		return
	}

	return stats, nil
}

// GetFeedbackTaskList 获取当前用户的反馈任务完成列表
func (feedbackService *FeedbackService) GetFeedbackTaskList(userID uint, info integralReq.FeedbackTaskListRequest) (list []integralRes.UserTaskItem, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&integral.SysUserTask{}).Where("user_id = ? AND task_id = ? AND status = ?",
		userID, config.TASK_FEEDBACK, integral.UserTaskStatusCompleted)

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	var userTasks []integral.SysUserTask
	err = db.Select("id, created_at, reward, remark").
		Limit(limit).Offset(offset).
		Order("created_at DESC").
		Find(&userTasks).Error

	if err != nil {
		return
	}

	// 转换为响应格式
	list = make([]integralRes.UserTaskItem, len(userTasks))
	for i, task := range userTasks {
		list[i] = integralRes.UserTaskItem{
			ID:          task.ID,
			CompletedAt: task.CreatedAt,
			Points:      task.Reward,
			Title:       task.Remark,
		}
	}

	return list, total, err
}
