package proxy

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
	"unicode/utf8"

	mcprouter "github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/jsonrpc"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/mcpserver"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/proxy"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// isValidUTF8 检查字节数组是否为有效的UTF-8编码
func isValidUTF8(data []byte) bool {
	return utf8.Valid(data)
}

// MCP is a handler for the mcp endpoint
func MCP(c echo.Context) error {
	ctx := proxy.GetSSEContext(c)
	if ctx == nil {
		return c.String(http.StatusInternalServerError, "Failed to get SSE context")
	}

	req := c.Request()
	method := req.Method
	header := req.Header

	// accept := header.Get("Accept")
	// accept: application/json, text/event-stream

	path := req.URL.Path

	sessionID := req.Header.Get("Mcp-Session-Id")

	// log.Printf("收到的header: %+v", c.Request().Header)
	// log.Printf("method: %s, accept: %s, sessionID: %s, path: %s\n", method, accept, sessionID, path)

	if method != http.MethodPost {
		// todo: return event-stream response when method is GET
		// todo: delete session when method is DELETE
		return c.String(http.StatusMethodNotAllowed, "Method Not Allowed")
	}

	key := c.Param("key")
	if key == "" {
		return c.String(http.StatusBadRequest, "Key is required")
	}

	// 兼容query、form、路径参数
	queryKey := c.QueryParam("key")
	queryToken := c.QueryParam("token")

	// 从 URL 路径中获取 token
	path = req.URL.Path
	parts := strings.Split(path, "/")
	if len(parts) < 4 {
		return c.String(http.StatusBadRequest, "Invalid path format")
	}
	pathToken := parts[3] // 获取路径中的最后一个参数作为 token

	// 优先使用查询参数中的token，如果没有则使用路径参数
	token := queryToken
	if token == "" {
		token = pathToken
	}

	// 查询 token 对应的 user_id
	k, err := service.ServiceGroupApp.McpServiceGroup.ApiKeyService.ValidateApiKey(token)
	if err != nil {
		return c.String(http.StatusUnauthorized, "Invalid apiKey")
	}

	serverConfig := mcpserver.GetServerConfig(key)
	if serverConfig == nil {
		return c.String(http.StatusBadRequest, "Invalid server config")
	}

	// 获取serverkey信息，用于获取KeyParamName
	serverkey, errKey := mcprouter.FindServerkeyByServerKey(key)
	if errKey != nil {
		serverkey = &mcprouter.Serverkey{KeyParamName: "key"}
	}

	// 判断是否需要走streamable_http_url代理
	if serverkey.StreamableHttpUrl != "" {
		// 构建完整的URL，包含查询参数
		fullHttpUrl, err := buildFullURL(serverkey.StreamableHttpUrl, c.Request().URL.RawQuery, queryKey, pathToken)
		if err != nil {
			log.Printf("[MCP-HTTP代理] URL构建失败: %v", err)
			return c.String(http.StatusBadRequest, "Invalid HTTP URL")
		}
		log.Printf("[MCP-HTTP代理] 即将请求: %s, headers: %+v", fullHttpUrl, c.Request().Header)
		headers := http.Header{}
		if serverkey.EnvJson != "" {
			var env map[string]interface{}
			_ = json.Unmarshal([]byte(serverkey.EnvJson), &env)
			for k, v := range env {
				headers.Set(k, fmt.Sprintf("%v", v))
			}
		}
		for k, v := range c.Request().Header {
			if len(v) > 0 {
				headers.Set(k, v[0])
			}
		}
		// Set the Accept header to include both application/json and text/event-stream
		// This is required by some third-party services for streamable HTTP URLs
		headers.Set("Accept", "application/json, text/event-stream")
		requestBody, _ := io.ReadAll(c.Request().Body)
		var reqObj map[string]interface{}
		_ = json.Unmarshal(requestBody, &reqObj)
		// 只在tools/call时做积分校验（预检查，实际扣除在返回结果后）
		var checkResult *PointsCheckResult
		if method, _ := reqObj["method"].(string); method == "tools/call" {
			// 检查是否需要扣积分
			dbEnv, _, normalizedHeader := parseEnvAndHeaders(serverkey, c.Request().Header)
			needDeductPoints := shouldDeductPoints(serverkey, normalizedHeader, dbEnv)

			if needDeductPoints {
				checkResult, _ = validatePointsForToolCall(c.Request().Context(), &jsonrpc.Request{
					BaseRequest: jsonrpc.BaseRequest{Method: method},
					Params:      reqObj["params"],
				}, serverkey.ServerUUID, fmt.Sprintf("%d", k.UserId))

				if checkResult.ErrorType != "" {
					// 积分检查失败，记录日志并返回错误
					if err := handlePointsCheckFailure(c.Request().Context(), checkResult, c, serverkey, nil); err != nil {
						return c.String(http.StatusForbidden, err.Error())
					}
				}
			}
		}
		client := &http.Client{
			Timeout: 30 * time.Second,
			// 禁用自动解压缩以避免乱码问题
			Transport: &http.Transport{
				DisableCompression: true,
			},
		}
		reqProxy, _ := http.NewRequest("POST", fullHttpUrl, bytes.NewReader(requestBody))
		reqProxy.Header = headers
		// 明确指定不接受压缩
		reqProxy.Header.Set("Accept-Encoding", "identity")
		resp, err := client.Do(reqProxy)
		if err != nil {
			log.Printf("[MCP-HTTP代理] 请求失败: %v", err)

			// 为HTTP请求失败记录server_logs
			if checkResult != nil && checkResult.ToolID != "" {
				errorMsg := fmt.Sprintf("HTTP请求失败: %v", err)
				tempProxyInfo := createErrorProxyInfo(c, serverkey, nil, checkResult, errorMsg)

				if err := createServerLogAndUpdateUsage(tempProxyInfo); err != nil {
					log.Printf("[MCP-HTTP代理] 创建请求失败日志记录失败: %v", err)
				}
			}

			return c.String(http.StatusBadGateway, "HTTP代理失败")
		}
		defer resp.Body.Close()

		// 记录响应状态和头部信息
		log.Printf("[MCP-HTTP代理] 响应状态: %d, Content-Type: %s, Content-Length: %s",
			resp.StatusCode, resp.Header.Get("Content-Type"), resp.Header.Get("Content-Length"))

		// 检查HTTP状态码 - 允许200和202状态码
		if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
			log.Printf("[MCP-HTTP代理] 收到错误状态码: %d", resp.StatusCode)

			// 为HTTP错误状态码记录server_logs
			if checkResult != nil && checkResult.ToolID != "" {
				errorMsg := fmt.Sprintf("HTTP代理失败，状态码: %d", resp.StatusCode)
				tempProxyInfo := createErrorProxyInfo(c, serverkey, nil, checkResult, errorMsg)

				if err := createServerLogAndUpdateUsage(tempProxyInfo); err != nil {
					log.Printf("[MCP-HTTP代理] 创建错误状态码日志记录失败: %v", err)
				}
			}

			return c.String(resp.StatusCode, fmt.Sprintf("上游服务返回错误: %d", resp.StatusCode))
		}

		// 对于202状态码，记录但继续处理
		if resp.StatusCode == http.StatusAccepted {
			log.Printf("[MCP-HTTP代理] 收到202状态码，这通常表示请求已被接受但可能返回空响应")
		}

		// 读取原始响应体
		rawResult, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Printf("[MCP-HTTP代理] 读取响应体失败: %v", err)

			// 为读取响应体失败记录server_logs
			if checkResult != nil && checkResult.ToolID != "" {
				errorMsg := fmt.Sprintf("读取响应体失败: %v", err)
				tempProxyInfo := createErrorProxyInfo(c, serverkey, nil, checkResult, errorMsg)

				if err := createServerLogAndUpdateUsage(tempProxyInfo); err != nil {
					log.Printf("[MCP-HTTP代理] 创建读取失败日志记录失败: %v", err)
				}
			}

			return c.String(http.StatusBadGateway, "读取响应失败")
		}

		// 检查响应是否为空 - 在MCP协议中，某些请求（如notifications）可能返回空响应
		if len(rawResult) == 0 {
			log.Printf("[MCP-HTTP代理] 收到空响应，这在MCP协议中是正常的")
			// 返回空的JSON响应而不是错误
			return c.Blob(http.StatusOK, "application/json", []byte("{}"))
		}

		log.Printf("[MCP-HTTP代理] 原始响应长度: %d, 内容: %s", len(rawResult), string(rawResult))

		// 智能检测响应格式并解析
		var result []byte
		var parsedResponse map[string]interface{}

		// 检查响应内容是否包含 SSE 格式的标识符
		rawStr := string(rawResult)

		// 检查是否为二进制数据或乱码
		if !isValidUTF8(rawResult) {
			log.Printf("[MCP-HTTP代理] 检测到非UTF-8响应，可能是压缩数据或乱码")

			// 为响应格式错误记录server_logs
			if checkResult != nil && checkResult.ToolID != "" {
				errorMsg := "响应格式错误，检测到非UTF-8响应"
				tempProxyInfo := createErrorProxyInfo(c, serverkey, nil, checkResult, errorMsg)

				if err := createServerLogAndUpdateUsage(tempProxyInfo); err != nil {
					log.Printf("[MCP-HTTP代理] 创建格式错误日志记录失败: %v", err)
				}
			}

			return c.String(http.StatusBadGateway, "响应格式错误")
		}

		if strings.Contains(rawStr, "id:") && strings.Contains(rawStr, "data:") {
			log.Printf("[MCP-HTTP代理] 检测到SSE格式响应，开始解析")

			// 按行分割响应内容
			lines := strings.Split(rawStr, "\n")
			var jsonData string

			for _, line := range lines {
				line = strings.TrimSpace(line)
				log.Printf("[MCP-HTTP代理] SSE行: %s", line)

				// 查找 data: 开头的行
				if strings.HasPrefix(line, "data:") {
					data := strings.TrimSpace(strings.TrimPrefix(line, "data:"))
					if data != "" && data != "[DONE]" {
						// 尝试解析为 JSON
						var testObj map[string]interface{}
						if err := json.Unmarshal([]byte(data), &testObj); err == nil {
							jsonData = data
							log.Printf("[MCP-HTTP代理] 找到有效的JSON数据: %s", data)
							break
						}
					}
				}
			}

			if jsonData == "" {
				log.Printf("[MCP-HTTP代理] SSE响应中未找到有效的JSON数据")

				// 为SSE解析失败记录server_logs
				if checkResult != nil && checkResult.ToolID != "" {
					errorMsg := "SSE响应解析失败，未找到有效的JSON数据"
					tempProxyInfo := createErrorProxyInfo(c, serverkey, nil, checkResult, errorMsg)

					if err := createServerLogAndUpdateUsage(tempProxyInfo); err != nil {
						log.Printf("[MCP-HTTP代理] 创建SSE解析失败日志记录失败: %v", err)
					}
				}

				return c.String(http.StatusBadGateway, "SSE响应解析失败")
			}

			// 使用提取的 JSON 数据
			result = []byte(jsonData)

			// 解析提取的 JSON 数据用于积分扣除逻辑
			if err := json.Unmarshal([]byte(jsonData), &parsedResponse); err != nil {
				log.Printf("[MCP-HTTP代理] SSE JSON解析失败: %v, 数据: %s", err, jsonData)
				return c.String(http.StatusBadGateway, "SSE JSON解析失败")
			}
		} else {
			// 按普通 JSON 响应处理
			log.Printf("[MCP-HTTP代理] 检测到JSON响应，开始解析")
			result = rawResult

			// 解析 JSON 用于积分扣除逻辑
			if err := json.Unmarshal(rawResult, &parsedResponse); err != nil {
				log.Printf("[MCP-HTTP代理] JSON响应解析失败: %v, 原始响应: %s", err, string(rawResult))
				// 即使解析失败，也返回原始响应，让客户端处理
				return c.Blob(http.StatusOK, "application/json", rawResult)
			}
		}

		log.Printf("[MCP-HTTP代理] 处理后的响应长度: %d", len(result))

		// 为成功的HTTP代理tools/call创建server_logs
		if checkResult != nil && checkResult.ToolID != "" {
			// 创建临时的ProxyInfo用于记录
			log.Printf("[MCP-HTTP代理] 准备记录server_logs，parsedResponse类型: %T, 值: %+v", parsedResponse, parsedResponse)
			tempProxyInfo := createSuccessProxyInfo(c, serverkey, nil, checkResult, parsedResponse)
			if err := createServerLogAndUpdateUsage(tempProxyInfo); err != nil {
				log.Printf("[MCP-HTTP代理] 创建成功日志记录失败: %v", err)
			} else {
				log.Printf("[MCP-HTTP代理] 成功记录server_logs，积分: %d", tempProxyInfo.Points)
			}
		}

		// 检查返回结果，如果需要扣积分且没有错误，则扣除积分
		if checkResult != nil && checkResult.ShouldDeduct && parsedResponse != nil {
			if err := deductPointsAfterToolCall(c.Request().Context(), checkResult, parsedResponse); err != nil {
				log.Printf("HTTP代理扣积分失败: %v", err)
			}
		}

		return c.Blob(http.StatusOK, "application/json", result)
	}

	// 解析数据库env_json，强制key大写并保留顺序
	dbEnv := map[string]string{}
	var envOrder []string
	if serverkey.EnvJson != "" {
		var rawEnv map[string]interface{}
		_ = json.Unmarshal([]byte(serverkey.EnvJson), &rawEnv)
		for k, v := range rawEnv {
			dbEnv[strings.ToUpper(k)] = fmt.Sprintf("%v", v)
		}
		// 用Decoder保留顺序
		decoder := json.NewDecoder(strings.NewReader(serverkey.EnvJson))
		_, _ = decoder.Token() // 跳过{
		for decoder.More() {
			tk, _ := decoder.Token()
			k := tk.(string)
			envOrder = append(envOrder, strings.ToUpper(k))
			decoder.Token() // 跳过值
		}
	}
	for k, v := range dbEnv {
		log.Printf("dbEnv key: %s, value: %s", k, v)
	}
	log.Printf("envOrder: %+v", envOrder)

	// 合并header，key不区分大小写
	normalizedHeader := map[string]string{}
	for k, v := range c.Request().Header {
		if len(v) > 0 {
			normalizedHeader[strings.ToUpper(k)] = v[0]
		}
	}

	// 获取key_param_name
	keyParamName := serverkey.KeyParamName
	if keyParamName == "" {
		keyParamName = "key"
	}
	keyParamName = strings.ToUpper(keyParamName)

	// 获取header和env_json里的key值
	headerKeyValue := normalizedHeader[keyParamName]
	envKeyValue := dbEnv[keyParamName]

	// 判断是否扣积分
	deductPoints := true
	if headerKeyValue != "" && headerKeyValue != envKeyValue {
		deductPoints = false // 用户自带key，不扣积分
	}
	// TODO: 在后续积分扣除逻辑处使用deductPoints变量
	_ = deductPoints // 避免linter错误，后续实际使用时可去掉

	// 拼接，header优先（不区分大小写），dbEnv也用大写key
	envParts := []string{}
	for _, k := range envOrder {
		v := normalizedHeader[k]
		if v == "" {
			v = dbEnv[k]
		}
		envParts = append(envParts, fmt.Sprintf("%s=%s", k, v))
	}
	cmd := serverConfig.Command
	if len(envParts) > 0 {
		cmd = fmt.Sprintf("%s %s", strings.Join(envParts, " "), serverConfig.Command)
	}

	// 处理autoApprove参数
	autoApprove := c.Request().Header.Get("Autoapprove")
	if autoApprove != "" {
		cmd = fmt.Sprintf("%s --auto-approve=%s", cmd, autoApprove)
	}

	serverConfig.Command = cmd
	fmt.Printf("%s\n", cmd)
	// 新增：每次调用cmd时增加日志
	log.Printf("[CMD_CALL] user_id=%d, key=%s, cmd=%s\n", k.UserId, key, cmd)

	request, err := ctx.GetJSONRPCRequest()
	if err != nil {
		return ctx.JSONRPCError(jsonrpc.ErrorParseError, nil)
	}

	// 在工具调用前检查积分（本地MCP调用）
	var checkResult *PointsCheckResult
	if request.Method == "tools/call" {
		// 检查是否需要扣积分
		dbEnv, _, normalizedHeader := parseEnvAndHeaders(serverkey, c.Request().Header)
		needDeductPoints := shouldDeductPoints(serverkey, normalizedHeader, dbEnv)

		if needDeductPoints {
			checkResult, _ = validatePointsForToolCall(c.Request().Context(), request, serverConfig.ServerUUID, fmt.Sprintf("%d", k.UserId))
			if checkResult.ErrorType != "" {
				// 积分检查失败，记录日志并返回错误
				if err := handlePointsCheckFailure(c.Request().Context(), checkResult, c, serverkey, serverConfig); err != nil {
					return ctx.JSONRPCError(jsonrpc.ErrorInvalidParams, request.ID)
				}
			}
		}
	}

	if request.Result != nil || request.Error != nil {
		// notification
		return ctx.JSONRPCAcceptResponse(nil)
	}

	proxyInfo := &proxy.ProxyInfo{
		ServerKey:          key,
		SessionID:          sessionID,
		ServerUUID:         serverConfig.ServerUUID,
		ServerConfigName:   serverConfig.ServerName,
		ServerShareProcess: serverConfig.ShareProcess,
		ServerType:         serverConfig.ServerType,
		ServerURL:          serverConfig.ServerURL,
		ServerCommand:      serverConfig.Command,
		ServerCommandHash:  serverConfig.CommandHash,
		RequestID:          header.Get("X-Request-ID"),
		RequestFrom:        header.Get("X-Request-From"),
		UserID:             fmt.Sprintf("%d", k.UserId), // 通过token查到的user_id
		IP:                 getClientIP(c),
	}

	// log.Printf("request: %+v\n", request)

	if request.Method == "initialize" {
		paramsB, _ := json.Marshal(request.Params)
		params := &jsonrpc.InitializeParams{}
		if err := json.Unmarshal(paramsB, params); err != nil {
			return ctx.JSONRPCError(jsonrpc.ErrorParseError, nil)
		}

		// start new session
		sessionID = uuid.New().String()

		proxyInfo.ConnectionTime = time.Now()
		proxyInfo.ClientName = params.ClientInfo.Name
		proxyInfo.ClientVersion = params.ClientInfo.Version
		proxyInfo.ProtocolVersion = params.ProtocolVersion
		proxyInfo.SessionID = sessionID

		err := proxy.StoreProxyInfo(sessionID, proxyInfo)

		log.Printf("store proxy info with client info: %s, %v, %s\n", sessionID, err, proxyInfo.ClientName)

		ctx.Response().Header().Set("Mcp-Session-Id", sessionID)
	} else {
		// not initialize request, check session
		if sessionID == "" {
			return c.String(http.StatusBadRequest, "Invalid session ID")
		}

		_proxyInfo, err := proxy.GetProxyInfo(sessionID)

		log.Printf("get proxy info from cache: %s, %v, %+v\n", sessionID, err, _proxyInfo)

		if _proxyInfo != nil && _proxyInfo.SessionID == sessionID {
			proxyInfo = _proxyInfo
		}
	}

	proxyInfo.JSONRPCVersion = request.JSONRPC
	proxyInfo.RequestMethod = request.Method
	proxyInfo.RequestTime = time.Now()
	proxyInfo.RequestParams = request.Params

	if request.ID != nil {
		proxyInfo.RequestID = request.ID
	}

	// 设置积分信息（如果是tools/call且需要扣积分）
	if checkResult != nil && checkResult.ToolID != "" && checkResult.ToolRecord.Points != nil {
		proxyInfo.ToolName = checkResult.ToolID
		proxyInfo.Points = *checkResult.ToolRecord.Points
		log.Printf("[本地MCP] 设置积分信息: ToolName=%s, Points=%d", checkResult.ToolID, *checkResult.ToolRecord.Points)
	}

	client, err := getOrCreateMCPClient(ctx, key, serverConfig, nil)
	if err != nil {
		fmt.Printf("获取MCP客户端失败: %v\n", err)
		return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
	}

	if client == nil {
		return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
	}

	response, err := client.ForwardMessage(request)

	// 记录请求完成时间和结果（无论成功失败）
	proxyInfo.ResponseTime = time.Now()
	proxyInfo.CostTime = proxyInfo.ResponseTime.Sub(proxyInfo.RequestTime).Milliseconds()

	if err != nil {
		// 记录错误信息
		proxyInfo.ResponseError = err.Error()

		// 为失败的tools/call创建server_logs
		if proxyInfo.RequestMethod == "tools/call" {
			if err := createServerLogAndUpdateUsage(proxyInfo); err != nil {
				log.Printf("[MCP] 创建失败日志记录失败: %v", err)
			}
		}

		handleClientError(ctx, key, serverConfig, nil, err)
		return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
	}

	if response != nil {
		if request.Method == "initialize" && response.Result != nil {
			resultB, _ := json.Marshal(response.Result)
			result := &jsonrpc.InitializeResult{}
			if err := json.Unmarshal(resultB, result); err != nil {
				fmt.Printf("unmarshal initialize result failed: %v\n", err)
				return ctx.JSONRPCError(jsonrpc.ErrorParseError, request.ID)
			}

			proxyInfo.ServerName = result.ServerInfo.Name
			proxyInfo.ServerVersion = result.ServerInfo.Version

			proxyInfo.ResponseResult = response

			err = proxy.StoreProxyInfo(sessionID, proxyInfo)

			log.Printf("store proxy info with server info: %s, %v, %s\n", sessionID, err, proxyInfo.ServerName)

			// 发送 notifications/initialized 通知
			go func() {
				time.Sleep(100 * time.Millisecond) // 短暂延迟确保 initialize 响应已发送
				notificationReq := &jsonrpc.Request{
					BaseRequest: jsonrpc.BaseRequest{
						JSONRPC: jsonrpc.JSONRPC_VERSION,
						Method:  jsonrpc.MethodInitializedNotification,
					},
					Params: map[string]interface{}{},
				}
				_, err := client.ForwardMessage(notificationReq)
				if err != nil {
					log.Printf("发送 notifications/initialized 失败: %v", err)
				} else {
					log.Printf("已发送 notifications/initialized 通知")
				}
			}()
		}
	}

	// 为成功的tools/call创建server_logs
	if proxyInfo.RequestMethod == "tools/call" {
		// 先设置响应结果，再记录日志
		proxyInfo.ResponseResult = response

		log.Printf("[本地MCP] 准备记录server_logs: ToolName=%s, Points=%d, ResponseResult类型: %T", proxyInfo.ToolName, proxyInfo.Points, response)
		if err := createServerLogAndUpdateUsage(proxyInfo); err != nil {
			log.Printf("[MCP] 创建成功日志记录失败: %v", err)
		} else {
			log.Printf("[本地MCP] 成功记录server_logs，积分: %d", proxyInfo.Points)
		}
	}

	proxyInfo.ResponseResult = response

	// 检查响应并决定是否扣除积分
	if checkResult != nil && checkResult.ShouldDeduct {
		if err := deductPointsAfterToolCall(c.Request().Context(), checkResult, response); err != nil {
			log.Printf("本地MCP扣积分失败: %v", err)
		}
	}

	proxyInfoB, _ := json.Marshal(proxyInfo)
	log.Printf("proxyInfo: %s\n", string(proxyInfoB))

	// notification
	if response == nil {
		return ctx.JSONRPCAcceptResponse(response)
	}

	return ctx.JSONRPCResponse(response)
}
