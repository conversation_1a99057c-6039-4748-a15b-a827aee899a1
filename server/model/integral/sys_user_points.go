package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// SysUserPoints 记录积分流水
// 用户每次积分变动都会插入一条记录
type SysUserPoints struct {
	global.GVA_MODEL
	UserID        uint   `json:"user_id" gorm:"index"`
	Change        int    `json:"change"`                             // 变动积分（负数为扣除）
	Reason        string `json:"reason"`                             // 变动原因
	Remark        string `json:"remark"`                             // 备注
	Type          string `json:"type" gorm:"type:varchar(16);index"` // mcp/llm/order/task/redeem等
	ProjectID     uint   `json:"project_id" gorm:"index"`            // type为mcp时，项目ID
	ProjectToolID uint   `json:"project_tool_id" gorm:"index"`       // type为mcp时，工具ID
	ModelID       uint   `json:"model_id" gorm:"index"`              // type为llm时，模型ID
	UsageLogID    uint   `json:"usage_log_id" gorm:"index"`          // type为llm时，使用记录id
	OrderID       uint   `json:"order_id" gorm:"index"`              // type为order时，订单id,暂无
	TaskID        uint   `json:"task_id" gorm:"index"`               // type为task时，任务ID
	UserTaskID    uint   `json:"user_task_id" gorm:"index"`          // type为task时，用户任务完成记录ID
	RedeemCodeID  uint   `json:"redeem_code_id" gorm:"index"`        // type为redeem时，兑换码ID
}

func (SysUserPoints) TableName() string {
	return "sys_user_points"
}
