package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type ProjectsSearch struct {
	StartCreatedAt *time.Time `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *time.Time `json:"endCreatedAt" form:"endCreatedAt"`
	Uuid           *string    `json:"uuid" form:"uuid"`
	Name           *string    `json:"name" form:"name"`
	IsOfficial     *bool      `json:"isOfficial" form:"isOfficial"`
	Status         *string    `json:"status" form:"status"`
	Category       *string    `json:"category" form:"category"`
	AllowCall      *bool      `json:"allowCall" form:"allowCall"`
	IsFeatured     *bool      `json:"isFeatured" form:"isFeatured"`
	IsEnabled      *bool      `json:"isEnabled" form:"isEnabled"` // 是否启用
	Platform       *string    `json:"platform" form:"platform"`   // 单平台筛选（mac/windows/linux）
	request.PageInfo
	Keyword        *string `json:"keyword" form:"keyword"`               // 关键词
	Package        *string `json:"package" form:"package"`               // 包名
	CallMethod     *string `json:"callMethod" form:"callMethod"`         // 调用方法：local-本地，online-线上
	OrderBy        *string `json:"orderBy" form:"orderBy"`               // 排序方式：latest-最新，hot-最热，random-随机
	Version        *string `json:"version" form:"version"`               // 版本号
	KeyParamName   *string `json:"keyParamName" form:"keyParamName"`     // key参数名
	HaveTools      *bool   `json:"haveTools" form:"haveTools"`           // 是否有工具
	EnableLongTask *bool   `json:"enableLongTask" form:"enableLongTask"` // 是否启用长任务
}

type ProjectFilter struct {
	Name       string `json:"name" form:"name"`
	Category   string `json:"category" form:"category"`
	Status     string `json:"status" form:"status"`
	IsOfficial *bool  `json:"isOfficial" form:"isOfficial"`
	request.PageInfo
}

// ProjectsSubmitRequest 用于提交mcp接口
// tools为工具数组，questions为提问问题数组
// userUuid为创建人
// 其他字段复用Projects

type ProjectsSubmitRequest struct {
	Name         string                   `json:"name"`
	Description  string                   `json:"description"`
	Questions    []string                 `json:"questions"`
	Tools        []map[string]interface{} `json:"tools"`                            // 工具数组，具体结构可根据前端定义
	Version      string                   `json:"version"`                          // 版本号
	KeyParamName string                   `json:"keyParamName" form:"keyParamName"` // key参数名
	// 其他字段可按需补充
}

// ProjectsCreateRequest 创建项目请求，支持第三方代理
type ProjectsCreateRequest struct {
	mcp.Projects // 嵌入 Projects 结构体，包含 ProxySseUrl 和 ProxyHttpUrl 字段
}

// ProjectsUpdateRequest 更新项目请求，支持第三方代理
type ProjectsUpdateRequest struct {
	mcp.Projects // 嵌入 Projects 结构体，包含 ProxySseUrl 和 ProxyHttpUrl 字段
}

// ProjectsSimpleCreateRequest 简化创建项目请求，不需要登录，直接包含tools
type ProjectsSimpleCreateRequest struct {
	mcp.Projects                          // 嵌入 Projects 结构体
	Tools        []map[string]interface{} `json:"tools"` // 工具数组
}
