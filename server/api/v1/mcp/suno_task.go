package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SunoTaskApi struct{}

// SunoCallback 接收Suno回调
// @Tags SunoTask
// @Summary 接收Suno回调
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mcp.SunoCallbackRequest true "回调数据"
// @Success 200 {object} response.Response{msg=string} "回调处理成功"
// @Router /suno/callback [post]
func (s *SunoTaskApi) SunoCallback(c *gin.Context) {
	var callbackReq mcp.SunoCallbackRequest
	err := c.ShouldBindJSON(&callbackReq)
	if err != nil {
		global.GVA_LOG.Error("绑定参数失败", zap.Error(err))
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 详细记录回调信息
	global.GVA_LOG.Info("收到Suno回调",
		zap.Int("code", callbackReq.Code),
		zap.String("msg", callbackReq.Msg),
		zap.String("taskId", callbackReq.Data.TaskID),
		zap.String("callbackType", callbackReq.Data.CallbackType),
		zap.Int("dataCount", len(callbackReq.Data.Data)),
		zap.Any("fullData", callbackReq.Data))

	// 输出原始请求体用于调试
	body, _ := c.GetRawData()
	global.GVA_LOG.Info("原始回调请求体", zap.String("rawBody", string(body)))

	err = service.ServiceGroupApp.McpServiceGroup.SunoTaskService.ProcessCallback(callbackReq)
	if err != nil {
		global.GVA_LOG.Error("处理回调失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
		response.FailWithMessage("处理回调失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("回调处理成功", c)
}

// SunoCallbackGenerate 接收Suno生成音乐回调（强制标记 complete）
// @Tags SunoTask
// @Summary 接收Suno生成音乐回调
// @accept application/json
// @Produce application/json
// @Param data body mcp.SunoCallbackRequest true "回调数据"
// @Success 200 {object} response.Response{msg=string}
// @Router /suno/callback/generate [post]
func (s *SunoTaskApi) SunoCallbackGenerate(c *gin.Context) {
	var callbackReq mcp.SunoCallbackRequest
	if err := c.ShouldBindJSON(&callbackReq); err != nil {
		global.GVA_LOG.Error("参数绑定失败", zap.Error(err))
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 验证必要字段
	if callbackReq.Data.TaskID == "" {
		global.GVA_LOG.Error("回调数据缺少task_id", zap.Any("callbackReq", callbackReq))
		response.FailWithMessage("回调数据缺少task_id", c)
		return
	}

	// 详细记录回调信息
	global.GVA_LOG.Info("收到Suno生成音乐回调",
		zap.String("taskId", callbackReq.Data.TaskID),
		zap.String("originalCallbackType", callbackReq.Data.CallbackType),
		zap.Int("code", callbackReq.Code),
		zap.String("msg", callbackReq.Msg),
		zap.Int("dataCount", len(callbackReq.Data.Data)),
		zap.Any("fullData", callbackReq.Data))

	// 强制设置类型为 complete（官方文档中的完成类型）
	callbackReq.Data.CallbackType = mcp.CallbackTypeComplete

	if err := service.ServiceGroupApp.McpServiceGroup.SunoTaskService.ProcessCallback(callbackReq); err != nil {
		global.GVA_LOG.Error("处理回调失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
		response.FailWithMessage("处理回调失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("回调处理成功", c)
}

// SunoCallbackCover 接收Suno上传并翻唱回调（强制标记 cover）
// @Tags SunoTask
// @Summary 接收Suno上传并翻唱回调
// @accept application/json
// @Produce application/json
// @Param data body mcp.SunoCallbackRequest true "回调数据"
// @Success 200 {object} response.Response{msg=string}
// @Router /suno/callback/upload-cover [post]
func (s *SunoTaskApi) SunoCallbackCover(c *gin.Context) {
	var callbackReq mcp.SunoCallbackRequest
	if err := c.ShouldBindJSON(&callbackReq); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	callbackReq.Data.CallbackType = mcp.CallbackTypeCover
	if err := service.ServiceGroupApp.McpServiceGroup.SunoTaskService.ProcessCallback(callbackReq); err != nil {
		global.GVA_LOG.Error("处理回调失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
		response.FailWithMessage("处理回调失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("回调处理成功", c)
}

// SunoCallbackExtend 接收Suno上传并扩展回调（强制标记 extend）
// @Tags SunoTask
// @Summary 接收Suno上传并扩展回调
// @accept application/json
// @Produce application/json
// @Param data body mcp.SunoCallbackRequest true "回调数据"
// @Success 200 {object} response.Response{msg=string}
// @Router /suno/callback/upload-extend [post]
func (s *SunoTaskApi) SunoCallbackExtend(c *gin.Context) {
	var callbackReq mcp.SunoCallbackRequest
	if err := c.ShouldBindJSON(&callbackReq); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	callbackReq.Data.CallbackType = mcp.CallbackTypeExtend
	if err := service.ServiceGroupApp.McpServiceGroup.SunoTaskService.ProcessCallback(callbackReq); err != nil {
		global.GVA_LOG.Error("处理回调失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
		response.FailWithMessage("处理回调失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("回调处理成功", c)
}

// QueryTaskProgress 查询任务进度
// @Tags SunoTask
// @Summary 查询任务进度
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskId path string true "任务ID"
// @Success 200 {object} response.Response{data=mcp.SunoTask} "查询成功"
// @Router /suno/task/{taskId} [get]
func (s *SunoTaskApi) QueryTaskProgress(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}

	result, err := service.ServiceGroupApp.McpServiceGroup.SunoTaskService.QueryTaskProgress(taskID)
	if err != nil {
		global.GVA_LOG.Error("查询任务进度失败", zap.Error(err), zap.String("taskId", taskID))
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	// 无论是否找到任务，都返回成功状态，让前端根据数据内容判断
	response.OkWithData(result, c)
}

// CreateSunoTask 创建Suno任务
// @Tags SunoTask
// @Summary 创建Suno任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mcpReq.CreateSunoTaskReq true "任务信息"
// @Success 200 {object} response.Response{data=mcp.SunoTask} "创建成功"
// @Router /suno/task [post]
func (s *SunoTaskApi) CreateSunoTask(c *gin.Context) {
	var req mcpReq.CreateSunoTaskReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	sunoTask, err := service.ServiceGroupApp.McpServiceGroup.SunoTaskService.CreateSunoTask(req)
	if err != nil {
		global.GVA_LOG.Error("创建任务失败", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithData(sunoTask, c)
}

// GetSunoTaskList 获取任务列表
// @Tags SunoTask
// @Summary 获取任务列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query mcpReq.SunoTaskSearchReq true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /suno/tasks [get]
func (s *SunoTaskApi) GetSunoTaskList(c *gin.Context) {
	var pageInfo mcpReq.SunoTaskSearchReq
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	list, total, err := service.ServiceGroupApp.McpServiceGroup.SunoTaskService.GetSunoTaskList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取任务列表失败", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetSunoTaskByID 根据ID获取任务详情
// @Tags SunoTask
// @Summary 根据ID获取任务详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskId path string true "任务ID"
// @Success 200 {object} response.Response{data=mcp.SunoTask} "获取成功"
// @Router /suno/task/detail/{taskId} [get]
func (s *SunoTaskApi) GetSunoTaskByID(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}

	sunoTask, err := service.ServiceGroupApp.McpServiceGroup.SunoTaskService.GetSunoTaskByID(taskID)
	if err != nil {
		global.GVA_LOG.Error("获取任务详情失败", zap.Error(err), zap.String("taskId", taskID))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(sunoTask, c)
}
